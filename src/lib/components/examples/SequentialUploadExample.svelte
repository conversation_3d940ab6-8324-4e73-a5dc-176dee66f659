<script lang="ts">
	import { FileUploadQueue, type UploadTask } from '$lib/services/fileUploadQueue';
	import type { FileUploadState } from '$lib/types/customer';

	export let customerId: number = 1;
	export let platformId: number = 1;

	let fileInput: HTMLInputElement;
	let uploadStates: FileUploadState[] = [];
	let uploadQueue: FileUploadQueue | null = null;
	let isUploading = false;

	// Initialize upload queue
	function initializeQueue() {
		if (!uploadQueue) {
			uploadQueue = new FileUploadQueue({
				maxRetries: 3,
				retryDelay: 1000,
				onProgress: handleProgress,
				onComplete: handleComplete,
				onError: handleError,
				onQueueComplete: handleQueueComplete
			});
		}
	}

	// Handle file selection
	async function handleFileSelect(event: Event) {
		const input = event.target as HTMLInputElement;
		if (!input.files || input.files.length === 0) return;

		const files = Array.from(input.files);
		await uploadFiles(files);
		input.value = ''; // Clear input
	}

	// Upload files sequentially
	async function uploadFiles(files: File[]) {
		initializeQueue();
		
		// Add tasks to queue
		const tasks = uploadQueue!.addFiles(files);
		
		// Add to UI state
		const newStates = tasks.map(task => ({
			file: task.file,
			status: 'uploading' as const,
			progress: 0,
			taskId: task.id
		}));

		uploadStates = [...uploadStates, ...newStates];
		isUploading = true;

		// Start processing
		try {
			await uploadQueue!.startProcessing(customerId, platformId);
		} catch (error) {
			console.error('Upload failed:', error);
		}
	}

	// Event handlers
	function handleProgress(task: UploadTask) {
		const index = uploadStates.findIndex(state => 
			(state as any).taskId === task.id
		);
		if (index !== -1) {
			uploadStates[index] = {
				...uploadStates[index],
				progress: task.progress
			};
			uploadStates = [...uploadStates];
		}
	}

	function handleComplete(task: UploadTask) {
		const index = uploadStates.findIndex(state => 
			(state as any).taskId === task.id
		);
		if (index !== -1) {
			uploadStates[index] = {
				...uploadStates[index],
				status: 'uploaded',
				progress: 100,
				uploadedMetadata: task.uploadedMetadata
			};
			uploadStates = [...uploadStates];
		}
	}

	function handleError(task: UploadTask) {
		const index = uploadStates.findIndex(state => 
			(state as any).taskId === task.id
		);
		if (index !== -1) {
			uploadStates[index] = {
				...uploadStates[index],
				status: 'failed',
				error: task.error
			};
			uploadStates = [...uploadStates];
		}
	}

	function handleQueueComplete(tasks: UploadTask[]) {
		isUploading = false;
		console.log('All uploads completed:', {
			total: tasks.length,
			successful: tasks.filter(t => t.status === 'completed').length,
			failed: tasks.filter(t => t.status === 'failed').length
		});
	}

	// Cancel upload
	function cancelUpload(index: number) {
		const state = uploadStates[index];
		if (state.taskId && uploadQueue) {
			uploadQueue.cancelTask(state.taskId);
			uploadStates[index] = {
				...uploadStates[index],
				status: 'failed',
				error: 'Cancelled'
			};
			uploadStates = [...uploadStates];
		}
	}

	// Remove file
	function removeFile(index: number) {
		uploadStates = uploadStates.filter((_, i) => i !== index);
	}

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<div class="p-6 max-w-2xl mx-auto">
	<h2 class="text-2xl font-bold mb-4">Sequential File Upload Example</h2>
	
	<!-- File Input -->
	<div class="mb-6">
		<input
			bind:this={fileInput}
			type="file"
			multiple
			on:change={handleFileSelect}
			class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
		/>
		<p class="text-sm text-gray-500 mt-2">Select multiple files to see sequential upload in action</p>
	</div>

	<!-- Upload Status -->
	{#if isUploading}
		<div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
			<div class="flex items-center">
				<div class="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent mr-2"></div>
				<span class="text-blue-700 font-medium">Processing uploads sequentially...</span>
			</div>
		</div>
	{/if}

	<!-- File List -->
	{#if uploadStates.length > 0}
		<div class="space-y-3">
			{#each uploadStates as state, index}
				<div class="border rounded-lg p-4 {
					state.status === 'uploaded' ? 'bg-green-50 border-green-200' :
					state.status === 'failed' ? 'bg-red-50 border-red-200' :
					'bg-blue-50 border-blue-200'
				}">
					<div class="flex items-center justify-between">
						<div class="flex-1">
							<div class="font-medium text-sm">{state.file.name}</div>
							<div class="text-xs text-gray-500">{formatFileSize(state.file.size)}</div>
							
							{#if state.status === 'uploading'}
								<div class="mt-2">
									<div class="flex justify-between text-xs mb-1">
										<span>Uploading...</span>
										<span>{state.progress || 0}%</span>
									</div>
									<div class="w-full bg-gray-200 rounded-full h-2">
										<div 
											class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
											style="width: {state.progress || 0}%">
										</div>
									</div>
								</div>
							{:else if state.status === 'uploaded'}
								<div class="text-xs text-green-600 mt-1">✓ Upload successful</div>
							{:else if state.status === 'failed'}
								<div class="text-xs text-red-600 mt-1">✗ {state.error || 'Upload failed'}</div>
							{/if}
						</div>
						
						<div class="ml-4">
							{#if state.status === 'uploading'}
								<button
									on:click={() => cancelUpload(index)}
									class="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200">
									Cancel
								</button>
							{:else}
								<button
									on:click={() => removeFile(index)}
									class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
									Remove
								</button>
							{/if}
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>
