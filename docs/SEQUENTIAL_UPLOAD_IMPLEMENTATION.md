# Sequential File Upload Implementation

This document describes the implementation of sequential file uploads with a frontend queue system for better performance and user experience.

## Overview

The sequential upload system processes multiple files one at a time rather than sending all files simultaneously to the backend. This approach provides:

- **Predictable resource usage** - Backend processes one file at a time
- **Better progress tracking** - Individual file progress with real-time updates
- **Improved error handling** - Per-file error reporting and retry mechanisms
- **User control** - Cancel individual uploads or entire queue
- **Better UX** - Visual progress bars and status indicators

## Architecture

### Components

1. **FileUploadQueue** (`src/lib/services/fileUploadQueue.ts`)
   - Core queue management service
   - Handles sequential processing, retries, and cancellation
   - Provides event callbacks for UI updates

2. **MessageInput.svelte** (Updated)
   - Integrates with FileUploadQueue
   - Provides UI for progress tracking and cancellation
   - Maintains backward compatibility

3. **FileUploadState** (Updated type)
   - Extended to include `taskId` for queue tracking
   - Maintains existing interface for compatibility

## Key Features

### Sequential Processing
```typescript
// Files are processed one at a time
while (this.queue.length > 0) {
    const task = this.queue.shift()!;
    await this.processTask(task, customerId, platformId);
}
```

### Progress Tracking
```typescript
xhr.upload.onprogress = (event) => {
    if (event.lengthComputable) {
        task.progress = Math.round((event.loaded / event.total) * 100);
        this.options.onProgress(task);
    }
};
```

### Retry Mechanism
```typescript
let attempt = 0;
while (attempt <= this.options.maxRetries) {
    try {
        // Upload attempt
        return;
    } catch (error) {
        attempt++;
        if (attempt > this.options.maxRetries) {
            // Mark as failed
        } else {
            // Wait before retry
            await this.delay(this.options.retryDelay * attempt);
        }
    }
}
```

### Cancellation Support
```typescript
// Cancel individual upload
cancelTask(taskId: string): boolean {
    if (this.currentTask?.id === taskId) {
        this.currentTask.status = 'cancelled';
        this.abortController?.abort();
        return true;
    }
    return false;
}
```

## Usage

### Basic Implementation

```typescript
import { FileUploadQueue } from '$lib/services/fileUploadQueue';

const uploadQueue = new FileUploadQueue({
    maxRetries: 3,
    retryDelay: 1000,
    onProgress: (task) => {
        console.log(`${task.file.name}: ${task.progress}%`);
    },
    onComplete: (task) => {
        console.log(`${task.file.name} uploaded successfully`);
    },
    onError: (task) => {
        console.error(`${task.file.name} failed: ${task.error}`);
    }
});

// Add files and start processing
const tasks = uploadQueue.addFiles(files);
await uploadQueue.startProcessing(customerId, platformId);
```

### Integration with Svelte Components

```svelte
<script lang="ts">
    import { FileUploadQueue, type UploadTask } from '$lib/services/fileUploadQueue';
    
    let uploadQueue: FileUploadQueue | null = null;
    let fileStates: FileUploadState[] = [];
    
    async function uploadFiles(files: File[]) {
        if (!uploadQueue) {
            uploadQueue = new FileUploadQueue({
                onProgress: handleProgress,
                onComplete: handleComplete,
                onError: handleError
            });
        }
        
        const tasks = uploadQueue.addFiles(files);
        fileStates = tasks.map(task => ({
            file: task.file,
            status: 'uploading',
            progress: 0,
            taskId: task.id
        }));
        
        await uploadQueue.startProcessing(customerId, platformId);
    }
</script>
```

## Configuration Options

### FileUploadQueue Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `maxRetries` | number | 3 | Maximum retry attempts per file |
| `retryDelay` | number | 1000 | Base delay between retries (ms) |
| `onProgress` | function | - | Called on upload progress updates |
| `onComplete` | function | - | Called when file upload completes |
| `onError` | function | - | Called when file upload fails |
| `onQueueComplete` | function | - | Called when entire queue finishes |

### Upload Task States

| State | Description |
|-------|-------------|
| `pending` | Task is queued but not started |
| `uploading` | File is currently being uploaded |
| `completed` | Upload finished successfully |
| `failed` | Upload failed after all retries |
| `cancelled` | Upload was cancelled by user |

## Error Handling

### Automatic Retries
- Failed uploads are automatically retried up to `maxRetries` times
- Exponential backoff: delay increases with each retry attempt
- Network errors, timeouts, and server errors trigger retries

### Error Reporting
```typescript
interface UploadTask {
    error?: string;        // Human-readable error message
    retryCount: number;    // Number of retry attempts made
    status: TaskStatus;    // Current task status
}
```

### User Feedback
- Real-time progress bars for each file
- Status indicators (uploading, completed, failed)
- Detailed error messages with retry information
- Cancel buttons for active uploads

## Performance Benefits

### Backend Resource Management
- **Memory usage**: Only one file processed at a time
- **CPU utilization**: Predictable, consistent load
- **Network bandwidth**: Controlled, sequential transfers
- **Database connections**: Reduced concurrent operations

### User Experience
- **Visual feedback**: Progress bars and status updates
- **Control**: Cancel individual or all uploads
- **Reliability**: Automatic retries for failed uploads
- **Responsiveness**: UI remains interactive during uploads

## Migration from Concurrent Uploads

### Before (Concurrent)
```typescript
// All files sent simultaneously
const formData = new FormData();
files.forEach((file, index) => {
    formData.append(`file_${index}`, file);
});
const response = await fetch(uploadUrl, { body: formData });
```

### After (Sequential)
```typescript
// Files processed one at a time
const uploadQueue = new FileUploadQueue(options);
const tasks = uploadQueue.addFiles(files);
await uploadQueue.startProcessing(customerId, platformId);
```

## Testing

### Unit Tests
- Queue management logic
- Retry mechanisms
- Cancellation handling
- Progress tracking

### Integration Tests
- File upload flow
- Error scenarios
- UI state management
- Event handling

### Performance Tests
- Memory usage under load
- Upload speed comparison
- Concurrent user scenarios
- Large file handling

## Future Enhancements

1. **Configurable Concurrency**: Allow limited concurrent uploads (e.g., 2-3 files)
2. **Priority Queue**: Upload important files first
3. **Bandwidth Throttling**: Limit upload speed to preserve network resources
4. **Resume Capability**: Resume interrupted uploads
5. **Compression**: Compress files before upload to reduce transfer time

## Troubleshooting

### Common Issues

1. **Uploads Stuck in Queue**
   - Check network connectivity
   - Verify backend endpoint availability
   - Review browser console for errors

2. **High Memory Usage**
   - Ensure files are not held in memory unnecessarily
   - Check for memory leaks in event handlers

3. **Slow Upload Performance**
   - Consider file size limits
   - Check network bandwidth
   - Review backend processing time

### Debug Mode
Enable detailed logging by setting debug flag:
```typescript
const uploadQueue = new FileUploadQueue({
    debug: true,  // Enable detailed console logging
    ...otherOptions
});
```
